2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 00:18:14 - sqlalchemy.engine.Engine - INFO - [generated in 0.01104s] {'id_1': 1}
2025-08-01 00:18:15 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:18:15 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 00:18:15 - sqlalchemy.engine.Engine - INFO - [cached since 0.3025s ago] {'id_1': 1}
2025-08-01 00:18:15 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 00:18:15 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 00:18:36 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 00:18:36 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 00:18:36 - sqlalchemy.engine.Engine - INFO - [cached since 22.01s ago] {'id_1': 1}
2025-08-01 00:18:36 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 10:49:09 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 10:49:09 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 10:49:09 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 10:49:09 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 10:49:09 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 10:49:09 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 10:49:10 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 10:49:10 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings
2025-08-01 10:49:10 - sqlalchemy.engine.Engine - INFO - [generated in 0.00105s] {}
2025-08-01 10:49:10 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 10:49:10 - sqlalchemy.engine.Engine - INFO - [generated in 0.00139s] {'param_1': 0, 'param_2': 2}
2025-08-01 10:49:10 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 10:49:12 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 10:49:12 - sqlalchemy.engine.Engine - INFO - SELECT count(rankings.id) AS count_1 
FROM rankings
2025-08-01 10:49:12 - sqlalchemy.engine.Engine - INFO - [cached since 2.741s ago] {}
2025-08-01 10:49:13 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
 LIMIT %(param_1)s, %(param_2)s
2025-08-01 10:49:13 - sqlalchemy.engine.Engine - INFO - [cached since 2.765s ago] {'param_1': 0, 'param_2': 2}
2025-08-01 10:49:13 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 10:49:17 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 10:49:17 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 10:49:17 - sqlalchemy.engine.Engine - INFO - [generated in 0.00083s] {'id_1': 1}
2025-08-01 10:49:17 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 11:56:26 - app.core.auth - ERROR - 获取当前用户ID失败: 
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - SELECT users.id, users.username, users.password_hash, users.nickname, users.avatar_url, users.email, users.phone, users.wechat_openid, users.wechat_unionid, users.`role`, users.is_active, users.is_verified, users.bio, users.level, users.points, users.location, users.user_number, users.gender, users.age, users.created_at, users.updated_at, users.last_login_at 
FROM users 
WHERE users.username = %(username_1)s
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - [generated in 0.00179s] {'username_1': 'admin'}
2025-08-01 11:57:29 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "D:\h5_code\yysls\.venv\lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - UPDATE users SET updated_at=CURRENT_TIMESTAMP, last_login_at=%(last_login_at)s WHERE users.id = %(users_id)s
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - [generated in 0.00082s] {'last_login_at': datetime.datetime(2025, 8, 1, 3, 57, 29, 922243), 'users_id': 1}
2025-08-01 11:57:29 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 11:57:30 - UserService - INFO - 用户认证成功 username=admin
2025-08-01 11:57:30 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 11:57:30 - sqlalchemy.engine.Engine - INFO - SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.nickname AS users_nickname, users.avatar_url AS users_avatar_url, users.email AS users_email, users.phone AS users_phone, users.wechat_openid AS users_wechat_openid, users.wechat_unionid AS users_wechat_unionid, users.`role` AS users_role, users.is_active AS users_is_active, users.is_verified AS users_is_verified, users.bio AS users_bio, users.level AS users_level, users.points AS users_points, users.location AS users_location, users.user_number AS users_user_number, users.gender AS users_gender, users.age AS users_age, users.created_at AS users_created_at, users.updated_at AS users_updated_at, users.last_login_at AS users_last_login_at 
FROM users 
WHERE users.id = %(pk_1)s
2025-08-01 11:57:30 - sqlalchemy.engine.Engine - INFO - [generated in 0.00158s] {'pk_1': 1}
2025-08-01 11:57:30 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 11:57:50 - app.api.v1.endpoints.upload - INFO - 用户1临时上传Excel文件: 榜单明细模板.xlsx -> a80ca6e93c7947f0a6f361bc43c5bcdf_1.xlsx
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:01:37 - sqlalchemy.engine.Engine - INFO - [generated in 0.00143s] {'id_1': 11}
2025-08-01 12:01:38 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:01:40 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:01:40 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:01:40 - sqlalchemy.engine.Engine - INFO - [cached since 2.949s ago] {'id_1': 1}
2025-08-01 12:01:41 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:02:21 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:02:21 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:02:21 - sqlalchemy.engine.Engine - INFO - [cached since 44.02s ago] {'id_1': 1}
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - [cached since 44.19s ago] {'id_1': 1}
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - [generated in 0.00083s] {'pk_1': 1}
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - SELECT ranking_details.id, ranking_details.ranking_id, ranking_details.rank_start, ranking_details.rank_end, ranking_details.completion_time, ranking_details.completion_seconds, ranking_details.participant_count, ranking_details.team_info, ranking_details.created_at, ranking_details.updated_at 
FROM ranking_details 
WHERE ranking_details.ranking_id = %(ranking_id_1)s
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - [generated in 0.00133s] {'ranking_id_1': 1}
2025-08-01 12:02:22 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:02:22 - RankingService - ERROR - 更新榜单并导入Excel失败 ranking_id=1: (pymysql.err.OperationalError) (1054, "Unknown column 'ranking_details.rank_start' in 'field list'")
[SQL: SELECT ranking_details.id, ranking_details.ranking_id, ranking_details.rank_start, ranking_details.rank_end, ranking_details.completion_time, ranking_details.completion_seconds, ranking_details.participant_count, ranking_details.team_info, ranking_details.created_at, ranking_details.updated_at 
FROM ranking_details 
WHERE ranking_details.ranking_id = %(ranking_id_1)s]
[parameters: {'ranking_id_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-01 12:11:39 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-01 12:11:39 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:11:39 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-01 12:11:39 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:11:39 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-01 12:11:39 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - [generated in 0.00161s] {'id_1': 1}
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(id_1)s
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - [cached since 0.09514s ago] {'id_1': 1}
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - SELECT rankings.id, rankings.name, rankings.period, rankings.ranking_type, rankings.start_time, rankings.end_time, rankings.team_size_limit, rankings.total_participants, rankings.status, rankings.created_at, rankings.updated_at 
FROM rankings 
WHERE rankings.id = %(pk_1)s
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - [generated in 0.00117s] {'pk_1': 1}
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - SELECT ranking_details.id, ranking_details.ranking_id, ranking_details.rank_start, ranking_details.rank_end, ranking_details.completion_time, ranking_details.completion_seconds, ranking_details.participant_count, ranking_details.team_info, ranking_details.team_name, ranking_details.created_at, ranking_details.updated_at 
FROM ranking_details 
WHERE ranking_details.ranking_id = %(ranking_id_1)s
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - [generated in 0.00096s] {'ranking_id_1': 1}
2025-08-01 12:11:40 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-01 12:11:40 - RankingService - ERROR - 更新榜单并导入Excel失败 ranking_id=1: (pymysql.err.OperationalError) (1054, "Unknown column 'ranking_details.rank_start' in 'field list'")
[SQL: SELECT ranking_details.id, ranking_details.ranking_id, ranking_details.rank_start, ranking_details.rank_end, ranking_details.completion_time, ranking_details.completion_seconds, ranking_details.participant_count, ranking_details.team_info, ranking_details.team_name, ranking_details.created_at, ranking_details.updated_at 
FROM ranking_details 
WHERE ranking_details.ranking_id = %(ranking_id_1)s]
[parameters: {'ranking_id_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-01 12:29:44 - app.core.auth - ERROR - 获取当前用户ID失败: 
