<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="@39.105.54.213">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.49">
    <root id="1">
      <DefaultCasing>lower/lower</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>yysls\\_ranking|schema||yysls||ALTER|G
yysls\\_ranking|schema||yysls||ALTER ROUTINE|G
yysls\\_ranking|schema||yysls||CREATE|G
yysls\\_ranking|schema||yysls||CREATE ROUTINE|G
yysls\\_ranking|schema||yysls||CREATE TEMPORARY TABLES|G
yysls\\_ranking|schema||yysls||CREATE VIEW|G
yysls\\_ranking|schema||yysls||DELETE|G
yysls\\_ranking|schema||yysls||DROP|G
yysls\\_ranking|schema||yysls||EVENT|G
yysls\\_ranking|schema||yysls||EXECUTE|G
yysls\\_ranking|schema||yysls||INDEX|G
yysls\\_ranking|schema||yysls||INSERT|G
yysls\\_ranking|schema||yysls||LOCK TABLES|G
yysls\\_ranking|schema||yysls||REFERENCES|G
yysls\\_ranking|schema||yysls||SELECT|G
yysls\\_ranking|schema||yysls||SHOW VIEW|G
yysls\\_ranking|schema||yysls||TRIGGER|G
yysls\\_ranking|schema||yysls||UPDATE|G
yysls_ranking|schema||yysls||ALTER|G
yysls_ranking|schema||yysls||ALTER ROUTINE|G
yysls_ranking|schema||yysls||CREATE|G
yysls_ranking|schema||yysls||CREATE ROUTINE|G
yysls_ranking|schema||yysls||CREATE TEMPORARY TABLES|G
yysls_ranking|schema||yysls||CREATE VIEW|G
yysls_ranking|schema||yysls||DELETE|G
yysls_ranking|schema||yysls||DROP|G
yysls_ranking|schema||yysls||EVENT|G
yysls_ranking|schema||yysls||EXECUTE|G
yysls_ranking|schema||yysls||INDEX|G
yysls_ranking|schema||yysls||INSERT|G
yysls_ranking|schema||yysls||LOCK TABLES|G
yysls_ranking|schema||yysls||REFERENCES|G
yysls_ranking|schema||yysls||SELECT|G
yysls_ranking|schema||yysls||SHOW VIEW|G
yysls_ranking|schema||yysls||TRIGGER|G
yysls_ranking|schema||yysls||UPDATE|G</Grants>
      <ServerVersion>8.0.43</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="3" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="4" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="5" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="6" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="7" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="10" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="18" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="20" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="21" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="23" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="24" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="25" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="26" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="27" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="28" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="29" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="30" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="31" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="32" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="33" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="36" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="37" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="38" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="39" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="42" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="43" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="44" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="45" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="46" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="47" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="48" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="49" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="50" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="51" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="52" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="55" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="56" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="57" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="58" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="59" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="61" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="67" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="69" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="71" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="73" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="74" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="77" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="79" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="80" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="81" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="82" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="83" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="84" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="85" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="86" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="87" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="88" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="89" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="95" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="116" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="117" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="118" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="124" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="144" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="145" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="146" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="152" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8mb3_general_ci">
      <Charset>utf8mb3</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="172" parent="1" name="utf8mb3_tolower_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8mb3_bin">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8mb3_unicode_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8mb3_icelandic_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8mb3_latvian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8mb3_romanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="178" parent="1" name="utf8mb3_slovenian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8mb3_polish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8mb3_estonian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8mb3_spanish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8mb3_swedish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8mb3_turkish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8mb3_czech_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8mb3_danish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8mb3_lithuanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8mb3_slovak_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8mb3_spanish2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8mb3_roman_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8mb3_persian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8mb3_esperanto_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8mb3_hungarian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8mb3_sinhala_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8mb3_german2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8mb3_croatian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8mb3_unicode_520_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8mb3_vietnamese_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8mb3_general_mysql500_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="224" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="225" parent="1" name="utf8mb4_0900_ai_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="226" parent="1" name="utf8mb4_de_pb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="227" parent="1" name="utf8mb4_is_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="228" parent="1" name="utf8mb4_lv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="229" parent="1" name="utf8mb4_ro_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="230" parent="1" name="utf8mb4_sl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="231" parent="1" name="utf8mb4_pl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="232" parent="1" name="utf8mb4_et_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="233" parent="1" name="utf8mb4_es_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="234" parent="1" name="utf8mb4_sv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="235" parent="1" name="utf8mb4_tr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="236" parent="1" name="utf8mb4_cs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="237" parent="1" name="utf8mb4_da_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="238" parent="1" name="utf8mb4_lt_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="239" parent="1" name="utf8mb4_sk_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="240" parent="1" name="utf8mb4_es_trad_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="241" parent="1" name="utf8mb4_la_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="242" parent="1" name="utf8mb4_eo_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="243" parent="1" name="utf8mb4_hu_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="244" parent="1" name="utf8mb4_hr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="245" parent="1" name="utf8mb4_vi_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="246" parent="1" name="utf8mb4_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="247" parent="1" name="utf8mb4_de_pb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="248" parent="1" name="utf8mb4_is_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="249" parent="1" name="utf8mb4_lv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="250" parent="1" name="utf8mb4_ro_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="251" parent="1" name="utf8mb4_sl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="252" parent="1" name="utf8mb4_pl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="253" parent="1" name="utf8mb4_et_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="254" parent="1" name="utf8mb4_es_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="255" parent="1" name="utf8mb4_sv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="256" parent="1" name="utf8mb4_tr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="257" parent="1" name="utf8mb4_cs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="258" parent="1" name="utf8mb4_da_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="259" parent="1" name="utf8mb4_lt_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="260" parent="1" name="utf8mb4_sk_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="261" parent="1" name="utf8mb4_es_trad_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="262" parent="1" name="utf8mb4_la_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="263" parent="1" name="utf8mb4_eo_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="264" parent="1" name="utf8mb4_hu_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="265" parent="1" name="utf8mb4_hr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="266" parent="1" name="utf8mb4_vi_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="267" parent="1" name="utf8mb4_ja_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="268" parent="1" name="utf8mb4_ja_0900_as_cs_ks">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="269" parent="1" name="utf8mb4_0900_as_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="270" parent="1" name="utf8mb4_ru_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="271" parent="1" name="utf8mb4_ru_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="272" parent="1" name="utf8mb4_zh_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="273" parent="1" name="utf8mb4_0900_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="274" parent="1" name="utf8mb4_nb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="275" parent="1" name="utf8mb4_nb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="276" parent="1" name="utf8mb4_nn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="277" parent="1" name="utf8mb4_nn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="278" parent="1" name="utf8mb4_sr_latn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="279" parent="1" name="utf8mb4_sr_latn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="280" parent="1" name="utf8mb4_bs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="281" parent="1" name="utf8mb4_bs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="282" parent="1" name="utf8mb4_bg_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="283" parent="1" name="utf8mb4_bg_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="284" parent="1" name="utf8mb4_gl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="285" parent="1" name="utf8mb4_gl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="286" parent="1" name="utf8mb4_mn_cyrl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="287" parent="1" name="utf8mb4_mn_cyrl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="288" parent="1" name="information_schema">
      <IntrospectionTimestamp>2025-08-01.07:31:35</IntrospectionTimestamp>
      <LocalIntrospectionTimestamp>2025-07-31.15:29:32</LocalIntrospectionTimestamp>
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="289" parent="1" name="performance_schema">
      <IntrospectionTimestamp>2025-08-01.07:31:45</IntrospectionTimestamp>
      <LocalIntrospectionTimestamp>2025-07-31.15:29:42</LocalIntrospectionTimestamp>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="290" parent="1" name="yysls_ranking">
      <IntrospectionTimestamp>2025-08-01.22:20:49</IntrospectionTimestamp>
      <LocalIntrospectionTimestamp>2025-08-01.04:23:53</LocalIntrospectionTimestamp>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </schema>
    <user id="291" parent="1" name="yysls"/>
    <table id="292" parent="288" name="FILES">
      <Engine>memory</Engine>
      <System>1</System>
    </table>
    <view id="293" parent="288" name="ADMINISTRABLE_ROLE_AUTHORIZATIONS">
      <SourceTextLength>721</SourceTextLength>
      <System>1</System>
    </view>
    <view id="294" parent="288" name="APPLICABLE_ROLES">
      <SourceTextLength>4768</SourceTextLength>
      <System>1</System>
    </view>
    <view id="295" parent="288" name="CHARACTER_SETS">
      <SourceTextLength>262</SourceTextLength>
      <System>1</System>
    </view>
    <view id="296" parent="288" name="CHECK_CONSTRAINTS">
      <SourceTextLength>562</SourceTextLength>
      <System>1</System>
    </view>
    <view id="297" parent="288" name="COLLATIONS">
      <SourceTextLength>475</SourceTextLength>
      <System>1</System>
    </view>
    <view id="298" parent="288" name="COLLATION_CHARACTER_SET_APPLICABILITY">
      <SourceTextLength>187</SourceTextLength>
      <System>1</System>
    </view>
    <view id="299" parent="288" name="COLUMNS">
      <SourceTextLength>3682</SourceTextLength>
      <System>1</System>
    </view>
    <view id="300" parent="288" name="COLUMNS_EXTENSIONS">
      <SourceTextLength>787</SourceTextLength>
      <System>1</System>
    </view>
    <view id="301" parent="288" name="COLUMN_STATISTICS">
      <SourceTextLength>383</SourceTextLength>
      <System>1</System>
    </view>
    <view id="302" parent="288" name="ENABLED_ROLES">
      <SourceTextLength>954</SourceTextLength>
      <System>1</System>
    </view>
    <view id="303" parent="288" name="EVENTS">
      <SourceTextLength>1793</SourceTextLength>
      <System>1</System>
    </view>
    <view id="304" parent="288" name="INNODB_DATAFILES">
      <SourceTextLength>377</SourceTextLength>
      <System>1</System>
    </view>
    <view id="305" parent="288" name="INNODB_FIELDS">
      <SourceTextLength>543</SourceTextLength>
      <System>1</System>
    </view>
    <view id="306" parent="288" name="INNODB_FOREIGN">
      <SourceTextLength>935</SourceTextLength>
      <System>1</System>
    </view>
    <view id="307" parent="288" name="INNODB_FOREIGN_COLS">
      <SourceTextLength>708</SourceTextLength>
      <System>1</System>
    </view>
    <view id="308" parent="288" name="INNODB_TABLESPACES_BRIEF">
      <SourceTextLength>677</SourceTextLength>
      <System>1</System>
    </view>
    <view id="309" parent="288" name="KEYWORDS">
      <SourceTextLength>11827</SourceTextLength>
      <System>1</System>
    </view>
    <view id="310" parent="288" name="KEY_COLUMN_USAGE">
      <SourceTextLength>2342</SourceTextLength>
      <System>1</System>
    </view>
    <view id="311" parent="288" name="PARAMETERS">
      <SourceTextLength>3063</SourceTextLength>
      <System>1</System>
    </view>
    <view id="312" parent="288" name="PARTITIONS">
      <SourceTextLength>5704</SourceTextLength>
      <System>1</System>
    </view>
    <view id="313" parent="288" name="REFERENTIAL_CONSTRAINTS">
      <SourceTextLength>861</SourceTextLength>
      <System>1</System>
    </view>
    <view id="314" parent="288" name="RESOURCE_GROUPS">
      <SourceTextLength>376</SourceTextLength>
      <System>1</System>
    </view>
    <view id="315" parent="288" name="ROLE_COLUMN_GRANTS">
      <SourceTextLength>4712</SourceTextLength>
      <System>1</System>
    </view>
    <view id="316" parent="288" name="ROLE_ROUTINE_GRANTS">
      <SourceTextLength>4580</SourceTextLength>
      <System>1</System>
    </view>
    <view id="317" parent="288" name="ROLE_TABLE_GRANTS">
      <SourceTextLength>4478</SourceTextLength>
      <System>1</System>
    </view>
    <view id="318" parent="288" name="ROUTINES">
      <SourceTextLength>4239</SourceTextLength>
      <System>1</System>
    </view>
    <view id="319" parent="288" name="SCHEMATA">
      <SourceTextLength>589</SourceTextLength>
      <System>1</System>
    </view>
    <view id="320" parent="288" name="SCHEMATA_EXTENSIONS">
      <SourceTextLength>324</SourceTextLength>
      <System>1</System>
    </view>
    <view id="321" parent="288" name="STATISTICS">
      <SourceTextLength>2536</SourceTextLength>
      <System>1</System>
    </view>
    <view id="322" parent="288" name="ST_GEOMETRY_COLUMNS">
      <SourceTextLength>763</SourceTextLength>
      <System>1</System>
    </view>
    <view id="323" parent="288" name="ST_SPATIAL_REFERENCE_SYSTEMS">
      <SourceTextLength>472</SourceTextLength>
      <System>1</System>
    </view>
    <view id="324" parent="288" name="ST_UNITS_OF_MEASURE">
      <SourceTextLength>2788</SourceTextLength>
      <System>1</System>
    </view>
    <view id="325" parent="288" name="TABLES">
      <SourceTextLength>4565</SourceTextLength>
      <System>1</System>
    </view>
    <view id="326" parent="288" name="TABLESPACES_EXTENSIONS">
      <SourceTextLength>123</SourceTextLength>
      <System>1</System>
    </view>
    <view id="327" parent="288" name="TABLES_EXTENSIONS">
      <SourceTextLength>486</SourceTextLength>
      <System>1</System>
    </view>
    <view id="328" parent="288" name="TABLE_CONSTRAINTS">
      <SourceTextLength>1424</SourceTextLength>
      <System>1</System>
    </view>
    <view id="329" parent="288" name="TABLE_CONSTRAINTS_EXTENSIONS">
      <SourceTextLength>619</SourceTextLength>
      <System>1</System>
    </view>
    <view id="330" parent="288" name="TRIGGERS">
      <SourceTextLength>1719</SourceTextLength>
      <System>1</System>
    </view>
    <view id="331" parent="288" name="USER_ATTRIBUTES">
      <SourceTextLength>253</SourceTextLength>
      <System>1</System>
    </view>
    <view id="332" parent="288" name="VIEWS">
      <SourceTextLength>1167</SourceTextLength>
      <System>1</System>
    </view>
    <view id="333" parent="288" name="VIEW_ROUTINE_USAGE">
      <SourceTextLength>1007</SourceTextLength>
      <System>1</System>
    </view>
    <view id="334" parent="288" name="VIEW_TABLE_USAGE">
      <SourceTextLength>810</SourceTextLength>
      <System>1</System>
    </view>
    <table id="335" parent="290" name="alembic_version">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="336" parent="290" name="broadcast_messages">
      <Comment>播报消息表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="337" parent="290" name="contents">
      <Comment>内容表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="338" parent="290" name="ranking_details">
      <Comment>榜单明细表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="339" parent="290" name="rankings">
      <Comment>榜单表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="340" parent="290" name="sponsors">
      <Comment>赞助商表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="341" parent="290" name="system_configs">
      <Comment>系统配置表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <table id="342" parent="290" name="users">
      <Comment>用户表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </table>
    <column id="343" parent="292" name="FILE_ID">
      <DasType>bigint|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="344" parent="292" name="FILE_NAME">
      <DasType>text|0s</DasType>
      <Position>2</Position>
      <CollationName>utf8mb3_bin</CollationName>
    </column>
    <column id="345" parent="292" name="FILE_TYPE">
      <DasType>varchar(256)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="346" parent="292" name="TABLESPACE_NAME">
      <DasType>varchar(268)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <CollationName>utf8mb3_bin</CollationName>
    </column>
    <column id="347" parent="292" name="TABLE_CATALOG">
      <DasType>char(0)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="348" parent="292" name="TABLE_SCHEMA">
      <DasType>binary(0)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="349" parent="292" name="TABLE_NAME">
      <DasType>binary(0)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="350" parent="292" name="LOGFILE_GROUP_NAME">
      <DasType>varchar(256)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="351" parent="292" name="LOGFILE_GROUP_NUMBER">
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="352" parent="292" name="ENGINE">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="353" parent="292" name="FULLTEXT_KEYS">
      <DasType>binary(0)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="354" parent="292" name="DELETED_ROWS">
      <DasType>binary(0)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="355" parent="292" name="UPDATE_COUNT">
      <DasType>binary(0)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="356" parent="292" name="FREE_EXTENTS">
      <DasType>bigint|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="357" parent="292" name="TOTAL_EXTENTS">
      <DasType>bigint|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="358" parent="292" name="EXTENT_SIZE">
      <DasType>bigint|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="359" parent="292" name="INITIAL_SIZE">
      <DasType>bigint|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="360" parent="292" name="MAXIMUM_SIZE">
      <DasType>bigint|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="361" parent="292" name="AUTOEXTEND_SIZE">
      <DasType>bigint|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="362" parent="292" name="CREATION_TIME">
      <DasType>binary(0)|0s</DasType>
      <Position>20</Position>
    </column>
    <column id="363" parent="292" name="LAST_UPDATE_TIME">
      <DasType>binary(0)|0s</DasType>
      <Position>21</Position>
    </column>
    <column id="364" parent="292" name="LAST_ACCESS_TIME">
      <DasType>binary(0)|0s</DasType>
      <Position>22</Position>
    </column>
    <column id="365" parent="292" name="RECOVER_TIME">
      <DasType>binary(0)|0s</DasType>
      <Position>23</Position>
    </column>
    <column id="366" parent="292" name="TRANSACTION_COUNTER">
      <DasType>binary(0)|0s</DasType>
      <Position>24</Position>
    </column>
    <column id="367" parent="292" name="VERSION">
      <DasType>bigint|0s</DasType>
      <Position>25</Position>
    </column>
    <column id="368" parent="292" name="ROW_FORMAT">
      <DasType>varchar(256)|0s</DasType>
      <Position>26</Position>
    </column>
    <column id="369" parent="292" name="TABLE_ROWS">
      <DasType>binary(0)|0s</DasType>
      <Position>27</Position>
    </column>
    <column id="370" parent="292" name="AVG_ROW_LENGTH">
      <DasType>binary(0)|0s</DasType>
      <Position>28</Position>
    </column>
    <column id="371" parent="292" name="DATA_LENGTH">
      <DasType>binary(0)|0s</DasType>
      <Position>29</Position>
    </column>
    <column id="372" parent="292" name="MAX_DATA_LENGTH">
      <DasType>binary(0)|0s</DasType>
      <Position>30</Position>
    </column>
    <column id="373" parent="292" name="INDEX_LENGTH">
      <DasType>binary(0)|0s</DasType>
      <Position>31</Position>
    </column>
    <column id="374" parent="292" name="DATA_FREE">
      <DasType>bigint|0s</DasType>
      <Position>32</Position>
    </column>
    <column id="375" parent="292" name="CREATE_TIME">
      <DasType>binary(0)|0s</DasType>
      <Position>33</Position>
    </column>
    <column id="376" parent="292" name="UPDATE_TIME">
      <DasType>binary(0)|0s</DasType>
      <Position>34</Position>
    </column>
    <column id="377" parent="292" name="CHECK_TIME">
      <DasType>binary(0)|0s</DasType>
      <Position>35</Position>
    </column>
    <column id="378" parent="292" name="CHECKSUM">
      <DasType>binary(0)|0s</DasType>
      <Position>36</Position>
    </column>
    <column id="379" parent="292" name="STATUS">
      <DasType>varchar(256)|0s</DasType>
      <Position>37</Position>
    </column>
    <column id="380" parent="292" name="EXTRA">
      <DasType>varchar(256)|0s</DasType>
      <Position>38</Position>
    </column>
    <column id="381" parent="293" name="USER">
      <DasType>varchar(97)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="382" parent="293" name="HOST">
      <DasType>varchar(256)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="383" parent="293" name="GRANTEE">
      <DasType>varchar(97)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="384" parent="293" name="GRANTEE_HOST">
      <DasType>varchar(256)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="385" parent="293" name="ROLE_NAME">
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="386" parent="293" name="ROLE_HOST">
      <DasType>varchar(256)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="387" parent="293" name="IS_GRANTABLE">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="388" parent="293" name="IS_DEFAULT">
      <DasType>varchar(3)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="389" parent="293" name="IS_MANDATORY">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="390" parent="294" name="USER">
      <DasType>varchar(97)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="391" parent="294" name="HOST">
      <DasType>varchar(256)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="392" parent="294" name="GRANTEE">
      <DasType>varchar(97)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="393" parent="294" name="GRANTEE_HOST">
      <DasType>varchar(256)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="394" parent="294" name="ROLE_NAME">
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="395" parent="294" name="ROLE_HOST">
      <DasType>varchar(256)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="396" parent="294" name="IS_GRANTABLE">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="397" parent="294" name="IS_DEFAULT">
      <DasType>varchar(3)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="398" parent="294" name="IS_MANDATORY">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="399" parent="295" name="CHARACTER_SET_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="400" parent="295" name="DEFAULT_COLLATE_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="401" parent="295" name="DESCRIPTION">
      <DasType>varchar(2048)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="402" parent="295" name="MAXLEN">
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="403" parent="296" name="CONSTRAINT_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="404" parent="296" name="CONSTRAINT_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="405" parent="296" name="CONSTRAINT_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="406" parent="296" name="CHECK_CLAUSE">
      <DasType>longtext|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="407" parent="297" name="COLLATION_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="408" parent="297" name="CHARACTER_SET_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="409" parent="297" name="ID">
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="410" parent="297" name="IS_DEFAULT">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="411" parent="297" name="IS_COMPILED">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="412" parent="297" name="SORTLEN">
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="413" parent="297" name="PAD_ATTRIBUTE">
      <DasType>enum(&apos;PAD SPACE&apos;, &apos;NO PAD&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="414" parent="298" name="COLLATION_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="415" parent="298" name="CHARACTER_SET_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="416" parent="299" name="TABLE_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="417" parent="299" name="TABLE_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="418" parent="299" name="TABLE_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="419" parent="299" name="COLUMN_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="420" parent="299" name="ORDINAL_POSITION">
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="421" parent="299" name="COLUMN_DEFAULT">
      <DasType>text|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="422" parent="299" name="IS_NULLABLE">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="423" parent="299" name="DATA_TYPE">
      <DasType>longtext|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="424" parent="299" name="CHARACTER_MAXIMUM_LENGTH">
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="425" parent="299" name="CHARACTER_OCTET_LENGTH">
      <DasType>bigint|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="426" parent="299" name="NUMERIC_PRECISION">
      <DasType>bigint unsigned|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="427" parent="299" name="NUMERIC_SCALE">
      <DasType>bigint unsigned|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="428" parent="299" name="DATETIME_PRECISION">
      <DasType>int unsigned|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="429" parent="299" name="CHARACTER_SET_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="430" parent="299" name="COLLATION_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="431" parent="299" name="COLUMN_TYPE">
      <DasType>mediumtext|0s</DasType>
      <NotNull>1</NotNull>
      <Position>16</Position>
    </column>
    <column id="432" parent="299" name="COLUMN_KEY">
      <DasType>enum(&apos;&apos;, &apos;PRI&apos;, &apos;UNI&apos;, &apos;MUL&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>17</Position>
    </column>
    <column id="433" parent="299" name="EXTRA">
      <DasType>varchar(256)|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="434" parent="299" name="PRIVILEGES">
      <DasType>varchar(154)|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="435" parent="299" name="COLUMN_COMMENT">
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>20</Position>
    </column>
    <column id="436" parent="299" name="GENERATION_EXPRESSION">
      <DasType>longtext|0s</DasType>
      <NotNull>1</NotNull>
      <Position>21</Position>
    </column>
    <column id="437" parent="299" name="SRS_ID">
      <DasType>int unsigned|0s</DasType>
      <Position>22</Position>
    </column>
    <column id="438" parent="300" name="TABLE_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="439" parent="300" name="TABLE_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="440" parent="300" name="TABLE_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="441" parent="300" name="COLUMN_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="442" parent="300" name="ENGINE_ATTRIBUTE">
      <DasType>json|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="443" parent="300" name="SECONDARY_ENGINE_ATTRIBUTE">
      <DasType>json|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="444" parent="301" name="SCHEMA_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="445" parent="301" name="TABLE_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="446" parent="301" name="COLUMN_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="447" parent="301" name="HISTOGRAM">
      <DasType>json|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="448" parent="302" name="ROLE_NAME">
      <DasType>varchar(255)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="449" parent="302" name="ROLE_HOST">
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="450" parent="302" name="IS_DEFAULT">
      <DasType>varchar(3)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="451" parent="302" name="IS_MANDATORY">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="452" parent="303" name="EVENT_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="453" parent="303" name="EVENT_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="454" parent="303" name="EVENT_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="455" parent="303" name="DEFINER">
      <DasType>varchar(288)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="456" parent="303" name="TIME_ZONE">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="457" parent="303" name="EVENT_BODY">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="458" parent="303" name="EVENT_DEFINITION">
      <DasType>longtext|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="459" parent="303" name="EVENT_TYPE">
      <DasType>varchar(9)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="460" parent="303" name="EXECUTE_AT">
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="461" parent="303" name="INTERVAL_VALUE">
      <DasType>varchar(256)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="462" parent="303" name="INTERVAL_FIELD">
      <DasType>enum(&apos;YEAR&apos;, &apos;QUARTER&apos;, &apos;MONTH&apos;, &apos;DAY&apos;, &apos;HOUR&apos;, &apos;MINUTE&apos;, &apos;WEEK&apos;, &apos;SECOND&apos;, &apos;MICROSECOND&apos;, &apos;YEAR_MONTH&apos;, &apos;DAY_HOUR&apos;, &apos;DAY_MINUTE&apos;, &apos;DAY_SECOND&apos;, &apos;HOUR_MINUTE&apos;, &apos;HOUR_SECOND&apos;, &apos;MINUTE_SECOND&apos;, &apos;DAY_MICROSECOND&apos;, &apos;HOUR_MICROSECOND&apos;, &apos;MINUTE_MICROSECOND&apos;, &apos;SECOND_MICROSECOND&apos;)|0e</DasType>
      <Position>11</Position>
    </column>
    <column id="463" parent="303" name="SQL_MODE">
      <DasType>set(&apos;REAL_AS_FLOAT&apos;, &apos;PIPES_AS_CONCAT&apos;, &apos;ANSI_QUOTES&apos;, &apos;IGNORE_SPACE&apos;, &apos;NOT_USED&apos;, &apos;ONLY_FULL_GROUP_BY&apos;, &apos;NO_UNSIGNED_SUBTRACTION&apos;, &apos;NO_DIR_IN_CREATE&apos;, &apos;NOT_USED_9&apos;, &apos;NOT_USED_10&apos;, &apos;NOT_USED_11&apos;, &apos;NOT_USED_12&apos;, &apos;NOT_USED_13&apos;, &apos;NOT_USED_14&apos;, &apos;NOT_USED_15&apos;, &apos;NOT_USED_16&apos;, &apos;NOT_USED_17&apos;, &apos;NOT_USED_18&apos;, &apos;ANSI&apos;, &apos;NO_AUTO_VALUE_ON_ZERO&apos;, &apos;NO_BACKSLASH_ESCAPES&apos;, &apos;STRICT_TRANS_TABLES&apos;, &apos;STRICT_ALL_TABLES&apos;, &apos;NO_ZERO_IN_DATE&apos;, &apos;NO_ZERO_DATE&apos;, &apos;ALLOW_INVALID_DATES&apos;, &apos;ERROR_FOR_DIVISION_BY_ZERO&apos;, &apos;TRADITIONAL&apos;, &apos;NOT_USED_29&apos;, &apos;HIGH_NOT_PRECEDENCE&apos;, &apos;NO_ENGINE_SUBSTITUTION&apos;, &apos;PAD_CHAR_TO_FULL_LENGTH&apos;, &apos;TIME_TRUNCATE_FRACTIONAL&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="464" parent="303" name="STARTS">
      <DasType>datetime|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="465" parent="303" name="ENDS">
      <DasType>datetime|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="466" parent="303" name="STATUS">
      <DasType>enum(&apos;ENABLED&apos;, &apos;DISABLED&apos;, &apos;SLAVESIDE_DISABLED&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <column id="467" parent="303" name="ON_COMPLETION">
      <DasType>varchar(12)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>16</Position>
    </column>
    <column id="468" parent="303" name="CREATED">
      <DasType>timestamp|0s</DasType>
      <NotNull>1</NotNull>
      <Position>17</Position>
    </column>
    <column id="469" parent="303" name="LAST_ALTERED">
      <DasType>timestamp|0s</DasType>
      <NotNull>1</NotNull>
      <Position>18</Position>
    </column>
    <column id="470" parent="303" name="LAST_EXECUTED">
      <DasType>datetime|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="471" parent="303" name="EVENT_COMMENT">
      <DasType>varchar(2048)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>20</Position>
    </column>
    <column id="472" parent="303" name="ORIGINATOR">
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>21</Position>
    </column>
    <column id="473" parent="303" name="CHARACTER_SET_CLIENT">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>22</Position>
    </column>
    <column id="474" parent="303" name="COLLATION_CONNECTION">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>23</Position>
    </column>
    <column id="475" parent="303" name="DATABASE_COLLATION">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>24</Position>
    </column>
    <column id="476" parent="304" name="SPACE">
      <DasType>varbinary(256)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="477" parent="304" name="PATH">
      <DasType>varchar(512)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="478" parent="305" name="INDEX_ID">
      <DasType>varbinary(256)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="479" parent="305" name="NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="480" parent="305" name="POS">
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="481" parent="306" name="ID">
      <DasType>varchar(129)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="482" parent="306" name="FOR_NAME">
      <DasType>varchar(129)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="483" parent="306" name="REF_NAME">
      <DasType>varchar(129)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="484" parent="306" name="N_COLS">
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="485" parent="306" name="TYPE">
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="486" parent="307" name="ID">
      <DasType>varchar(129)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="487" parent="307" name="FOR_COL_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="488" parent="307" name="REF_COL_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="489" parent="307" name="POS">
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="490" parent="308" name="SPACE">
      <DasType>varbinary(256)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="491" parent="308" name="NAME">
      <DasType>varchar(268)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="492" parent="308" name="PATH">
      <DasType>varchar(512)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="493" parent="308" name="FLAG">
      <DasType>varbinary(256)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="494" parent="308" name="SPACE_TYPE">
      <DasType>varchar(7)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="495" parent="309" name="WORD">
      <DasType>varchar(128)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="496" parent="309" name="RESERVED">
      <DasType>int|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="497" parent="310" name="CONSTRAINT_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="498" parent="310" name="CONSTRAINT_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="499" parent="310" name="CONSTRAINT_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="500" parent="310" name="TABLE_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="501" parent="310" name="TABLE_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="502" parent="310" name="TABLE_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="503" parent="310" name="COLUMN_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="504" parent="310" name="ORDINAL_POSITION">
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="505" parent="310" name="POSITION_IN_UNIQUE_CONSTRAINT">
      <DasType>int unsigned|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="506" parent="310" name="REFERENCED_TABLE_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="507" parent="310" name="REFERENCED_TABLE_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="508" parent="310" name="REFERENCED_COLUMN_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="509" parent="311" name="SPECIFIC_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="510" parent="311" name="SPECIFIC_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="511" parent="311" name="SPECIFIC_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="512" parent="311" name="ORDINAL_POSITION">
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="513" parent="311" name="PARAMETER_MODE">
      <DasType>varchar(5)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="514" parent="311" name="PARAMETER_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="515" parent="311" name="DATA_TYPE">
      <DasType>longtext|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="516" parent="311" name="CHARACTER_MAXIMUM_LENGTH">
      <DasType>bigint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="517" parent="311" name="CHARACTER_OCTET_LENGTH">
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="518" parent="311" name="NUMERIC_PRECISION">
      <DasType>int unsigned|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="519" parent="311" name="NUMERIC_SCALE">
      <DasType>bigint|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="520" parent="311" name="DATETIME_PRECISION">
      <DasType>int unsigned|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="521" parent="311" name="CHARACTER_SET_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="522" parent="311" name="COLLATION_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="523" parent="311" name="DTD_IDENTIFIER">
      <DasType>mediumtext|0s</DasType>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <column id="524" parent="311" name="ROUTINE_TYPE">
      <DasType>enum(&apos;FUNCTION&apos;, &apos;PROCEDURE&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>16</Position>
    </column>
    <column id="525" parent="312" name="TABLE_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="526" parent="312" name="TABLE_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="527" parent="312" name="TABLE_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="528" parent="312" name="PARTITION_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="529" parent="312" name="SUBPARTITION_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="530" parent="312" name="PARTITION_ORDINAL_POSITION">
      <DasType>int unsigned|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="531" parent="312" name="SUBPARTITION_ORDINAL_POSITION">
      <DasType>int unsigned|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="532" parent="312" name="PARTITION_METHOD">
      <DasType>varchar(13)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="533" parent="312" name="SUBPARTITION_METHOD">
      <DasType>varchar(13)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="534" parent="312" name="PARTITION_EXPRESSION">
      <DasType>varchar(2048)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="535" parent="312" name="SUBPARTITION_EXPRESSION">
      <DasType>varchar(2048)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="536" parent="312" name="PARTITION_DESCRIPTION">
      <DasType>text|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="537" parent="312" name="TABLE_ROWS">
      <DasType>bigint unsigned|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="538" parent="312" name="AVG_ROW_LENGTH">
      <DasType>bigint unsigned|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="539" parent="312" name="DATA_LENGTH">
      <DasType>bigint unsigned|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="540" parent="312" name="MAX_DATA_LENGTH">
      <DasType>bigint unsigned|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="541" parent="312" name="INDEX_LENGTH">
      <DasType>bigint unsigned|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="542" parent="312" name="DATA_FREE">
      <DasType>bigint unsigned|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="543" parent="312" name="CREATE_TIME">
      <DasType>timestamp|0s</DasType>
      <NotNull>1</NotNull>
      <Position>19</Position>
    </column>
    <column id="544" parent="312" name="UPDATE_TIME">
      <DasType>datetime|0s</DasType>
      <Position>20</Position>
    </column>
    <column id="545" parent="312" name="CHECK_TIME">
      <DasType>datetime|0s</DasType>
      <Position>21</Position>
    </column>
    <column id="546" parent="312" name="CHECKSUM">
      <DasType>bigint|0s</DasType>
      <Position>22</Position>
    </column>
    <column id="547" parent="312" name="PARTITION_COMMENT">
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>23</Position>
    </column>
    <column id="548" parent="312" name="NODEGROUP">
      <DasType>varchar(256)|0s</DasType>
      <Position>24</Position>
    </column>
    <column id="549" parent="312" name="TABLESPACE_NAME">
      <DasType>varchar(268)|0s</DasType>
      <Position>25</Position>
    </column>
    <column id="550" parent="313" name="CONSTRAINT_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="551" parent="313" name="CONSTRAINT_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="552" parent="313" name="CONSTRAINT_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="553" parent="313" name="UNIQUE_CONSTRAINT_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="554" parent="313" name="UNIQUE_CONSTRAINT_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="555" parent="313" name="UNIQUE_CONSTRAINT_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="556" parent="313" name="MATCH_OPTION">
      <DasType>enum(&apos;NONE&apos;, &apos;PARTIAL&apos;, &apos;FULL&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="557" parent="313" name="UPDATE_RULE">
      <DasType>enum(&apos;NO ACTION&apos;, &apos;RESTRICT&apos;, &apos;CASCADE&apos;, &apos;SET NULL&apos;, &apos;SET DEFAULT&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="558" parent="313" name="DELETE_RULE">
      <DasType>enum(&apos;NO ACTION&apos;, &apos;RESTRICT&apos;, &apos;CASCADE&apos;, &apos;SET NULL&apos;, &apos;SET DEFAULT&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="559" parent="313" name="TABLE_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="560" parent="313" name="REFERENCED_TABLE_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="561" parent="314" name="RESOURCE_GROUP_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="562" parent="314" name="RESOURCE_GROUP_TYPE">
      <DasType>enum(&apos;SYSTEM&apos;, &apos;USER&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="563" parent="314" name="RESOURCE_GROUP_ENABLED">
      <DasType>tinyint(1)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="564" parent="314" name="VCPU_IDS">
      <DasType>blob|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="565" parent="314" name="THREAD_PRIORITY">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="566" parent="315" name="GRANTOR">
      <DasType>varchar(97)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="567" parent="315" name="GRANTOR_HOST">
      <DasType>varchar(256)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="568" parent="315" name="GRANTEE">
      <DasType>char(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="569" parent="315" name="GRANTEE_HOST">
      <DasType>char(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="570" parent="315" name="TABLE_CATALOG">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="571" parent="315" name="TABLE_SCHEMA">
      <DasType>char(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="572" parent="315" name="TABLE_NAME">
      <DasType>char(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="573" parent="315" name="COLUMN_NAME">
      <DasType>char(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="574" parent="315" name="PRIVILEGE_TYPE">
      <DasType>set(&apos;Select&apos;, &apos;Insert&apos;, &apos;Update&apos;, &apos;References&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="575" parent="315" name="IS_GRANTABLE">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="576" parent="316" name="GRANTOR">
      <DasType>varchar(97)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="577" parent="316" name="GRANTOR_HOST">
      <DasType>varchar(256)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="578" parent="316" name="GRANTEE">
      <DasType>char(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="579" parent="316" name="GRANTEE_HOST">
      <DasType>char(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="580" parent="316" name="SPECIFIC_CATALOG">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="581" parent="316" name="SPECIFIC_SCHEMA">
      <DasType>char(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="582" parent="316" name="SPECIFIC_NAME">
      <DasType>char(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="583" parent="316" name="ROUTINE_CATALOG">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="584" parent="316" name="ROUTINE_SCHEMA">
      <DasType>char(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="585" parent="316" name="ROUTINE_NAME">
      <DasType>char(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="586" parent="316" name="PRIVILEGE_TYPE">
      <DasType>set(&apos;Execute&apos;, &apos;Alter Routine&apos;, &apos;Grant&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="587" parent="316" name="IS_GRANTABLE">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="588" parent="317" name="GRANTOR">
      <DasType>varchar(97)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="589" parent="317" name="GRANTOR_HOST">
      <DasType>varchar(256)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="590" parent="317" name="GRANTEE">
      <DasType>char(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="591" parent="317" name="GRANTEE_HOST">
      <DasType>char(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="592" parent="317" name="TABLE_CATALOG">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="593" parent="317" name="TABLE_SCHEMA">
      <DasType>char(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="594" parent="317" name="TABLE_NAME">
      <DasType>char(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="595" parent="317" name="PRIVILEGE_TYPE">
      <DasType>set(&apos;Select&apos;, &apos;Insert&apos;, &apos;Update&apos;, &apos;Delete&apos;, &apos;Create&apos;, &apos;Drop&apos;, &apos;Grant&apos;, &apos;References&apos;, &apos;Index&apos;, &apos;Alter&apos;, &apos;Create View&apos;, &apos;Show view&apos;, &apos;Trigger&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="596" parent="317" name="IS_GRANTABLE">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="597" parent="318" name="SPECIFIC_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="598" parent="318" name="ROUTINE_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="599" parent="318" name="ROUTINE_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="600" parent="318" name="ROUTINE_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="601" parent="318" name="ROUTINE_TYPE">
      <DasType>enum(&apos;FUNCTION&apos;, &apos;PROCEDURE&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="602" parent="318" name="DATA_TYPE">
      <DasType>longtext|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="603" parent="318" name="CHARACTER_MAXIMUM_LENGTH">
      <DasType>bigint|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="604" parent="318" name="CHARACTER_OCTET_LENGTH">
      <DasType>bigint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="605" parent="318" name="NUMERIC_PRECISION">
      <DasType>int unsigned|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="606" parent="318" name="NUMERIC_SCALE">
      <DasType>int unsigned|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="607" parent="318" name="DATETIME_PRECISION">
      <DasType>int unsigned|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="608" parent="318" name="CHARACTER_SET_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="609" parent="318" name="COLLATION_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="610" parent="318" name="DTD_IDENTIFIER">
      <DasType>longtext|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="611" parent="318" name="ROUTINE_BODY">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <column id="612" parent="318" name="ROUTINE_DEFINITION">
      <DasType>longtext|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="613" parent="318" name="EXTERNAL_NAME">
      <DasType>binary(0)|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="614" parent="318" name="EXTERNAL_LANGUAGE">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>18</Position>
    </column>
    <column id="615" parent="318" name="PARAMETER_STYLE">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>19</Position>
    </column>
    <column id="616" parent="318" name="IS_DETERMINISTIC">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>20</Position>
    </column>
    <column id="617" parent="318" name="SQL_DATA_ACCESS">
      <DasType>enum(&apos;CONTAINS SQL&apos;, &apos;NO SQL&apos;, &apos;READS SQL DATA&apos;, &apos;MODIFIES SQL DATA&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>21</Position>
    </column>
    <column id="618" parent="318" name="SQL_PATH">
      <DasType>binary(0)|0s</DasType>
      <Position>22</Position>
    </column>
    <column id="619" parent="318" name="SECURITY_TYPE">
      <DasType>enum(&apos;DEFAULT&apos;, &apos;INVOKER&apos;, &apos;DEFINER&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>23</Position>
    </column>
    <column id="620" parent="318" name="CREATED">
      <DasType>timestamp|0s</DasType>
      <NotNull>1</NotNull>
      <Position>24</Position>
    </column>
    <column id="621" parent="318" name="LAST_ALTERED">
      <DasType>timestamp|0s</DasType>
      <NotNull>1</NotNull>
      <Position>25</Position>
    </column>
    <column id="622" parent="318" name="SQL_MODE">
      <DasType>set(&apos;REAL_AS_FLOAT&apos;, &apos;PIPES_AS_CONCAT&apos;, &apos;ANSI_QUOTES&apos;, &apos;IGNORE_SPACE&apos;, &apos;NOT_USED&apos;, &apos;ONLY_FULL_GROUP_BY&apos;, &apos;NO_UNSIGNED_SUBTRACTION&apos;, &apos;NO_DIR_IN_CREATE&apos;, &apos;NOT_USED_9&apos;, &apos;NOT_USED_10&apos;, &apos;NOT_USED_11&apos;, &apos;NOT_USED_12&apos;, &apos;NOT_USED_13&apos;, &apos;NOT_USED_14&apos;, &apos;NOT_USED_15&apos;, &apos;NOT_USED_16&apos;, &apos;NOT_USED_17&apos;, &apos;NOT_USED_18&apos;, &apos;ANSI&apos;, &apos;NO_AUTO_VALUE_ON_ZERO&apos;, &apos;NO_BACKSLASH_ESCAPES&apos;, &apos;STRICT_TRANS_TABLES&apos;, &apos;STRICT_ALL_TABLES&apos;, &apos;NO_ZERO_IN_DATE&apos;, &apos;NO_ZERO_DATE&apos;, &apos;ALLOW_INVALID_DATES&apos;, &apos;ERROR_FOR_DIVISION_BY_ZERO&apos;, &apos;TRADITIONAL&apos;, &apos;NOT_USED_29&apos;, &apos;HIGH_NOT_PRECEDENCE&apos;, &apos;NO_ENGINE_SUBSTITUTION&apos;, &apos;PAD_CHAR_TO_FULL_LENGTH&apos;, &apos;TIME_TRUNCATE_FRACTIONAL&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>26</Position>
    </column>
    <column id="623" parent="318" name="ROUTINE_COMMENT">
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>27</Position>
    </column>
    <column id="624" parent="318" name="DEFINER">
      <DasType>varchar(288)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>28</Position>
    </column>
    <column id="625" parent="318" name="CHARACTER_SET_CLIENT">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>29</Position>
    </column>
    <column id="626" parent="318" name="COLLATION_CONNECTION">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>30</Position>
    </column>
    <column id="627" parent="318" name="DATABASE_COLLATION">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>31</Position>
    </column>
    <column id="628" parent="319" name="CATALOG_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="629" parent="319" name="SCHEMA_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="630" parent="319" name="DEFAULT_CHARACTER_SET_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="631" parent="319" name="DEFAULT_COLLATION_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="632" parent="319" name="SQL_PATH">
      <DasType>binary(0)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="633" parent="319" name="DEFAULT_ENCRYPTION">
      <DasType>enum(&apos;NO&apos;, &apos;YES&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="634" parent="320" name="CATALOG_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="635" parent="320" name="SCHEMA_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="636" parent="320" name="OPTIONS">
      <DasType>varchar(256)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="637" parent="321" name="TABLE_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="638" parent="321" name="TABLE_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="639" parent="321" name="TABLE_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="640" parent="321" name="NON_UNIQUE">
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="641" parent="321" name="INDEX_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="642" parent="321" name="INDEX_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="643" parent="321" name="SEQ_IN_INDEX">
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="644" parent="321" name="COLUMN_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="645" parent="321" name="COLLATION">
      <DasType>varchar(1)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="646" parent="321" name="CARDINALITY">
      <DasType>bigint|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="647" parent="321" name="SUB_PART">
      <DasType>bigint|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="648" parent="321" name="PACKED">
      <DasType>binary(0)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="649" parent="321" name="NULLABLE">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <column id="650" parent="321" name="INDEX_TYPE">
      <DasType>varchar(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>14</Position>
    </column>
    <column id="651" parent="321" name="COMMENT">
      <DasType>varchar(8)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <column id="652" parent="321" name="INDEX_COMMENT">
      <DasType>varchar(2048)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>16</Position>
    </column>
    <column id="653" parent="321" name="IS_VISIBLE">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>17</Position>
    </column>
    <column id="654" parent="321" name="EXPRESSION">
      <DasType>longtext|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="655" parent="322" name="TABLE_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="656" parent="322" name="TABLE_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="657" parent="322" name="TABLE_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="658" parent="322" name="COLUMN_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="659" parent="322" name="SRS_NAME">
      <DasType>varchar(80)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="660" parent="322" name="SRS_ID">
      <DasType>int unsigned|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="661" parent="322" name="GEOMETRY_TYPE_NAME">
      <DasType>longtext|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="662" parent="323" name="SRS_NAME">
      <DasType>varchar(80)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="663" parent="323" name="SRS_ID">
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="664" parent="323" name="ORGANIZATION">
      <DasType>varchar(256)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="665" parent="323" name="ORGANIZATION_COORDSYS_ID">
      <DasType>int unsigned|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="666" parent="323" name="DEFINITION">
      <DasType>varchar(4096)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="667" parent="323" name="DESCRIPTION">
      <DasType>varchar(2048)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="668" parent="324" name="UNIT_NAME">
      <DasType>varchar(255)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="669" parent="324" name="UNIT_TYPE">
      <DasType>varchar(7)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="670" parent="324" name="CONVERSION_FACTOR">
      <DasType>double|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="671" parent="324" name="DESCRIPTION">
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="672" parent="325" name="TABLE_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="673" parent="325" name="TABLE_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="674" parent="325" name="TABLE_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="675" parent="325" name="TABLE_TYPE">
      <DasType>enum(&apos;BASE TABLE&apos;, &apos;VIEW&apos;, &apos;SYSTEM VIEW&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="676" parent="325" name="ENGINE">
      <DasType>varchar(64)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="677" parent="325" name="VERSION">
      <DasType>int|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="678" parent="325" name="ROW_FORMAT">
      <DasType>enum(&apos;Fixed&apos;, &apos;Dynamic&apos;, &apos;Compressed&apos;, &apos;Redundant&apos;, &apos;Compact&apos;, &apos;Paged&apos;)|0e</DasType>
      <Position>7</Position>
    </column>
    <column id="679" parent="325" name="TABLE_ROWS">
      <DasType>bigint unsigned|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="680" parent="325" name="AVG_ROW_LENGTH">
      <DasType>bigint unsigned|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="681" parent="325" name="DATA_LENGTH">
      <DasType>bigint unsigned|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="682" parent="325" name="MAX_DATA_LENGTH">
      <DasType>bigint unsigned|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="683" parent="325" name="INDEX_LENGTH">
      <DasType>bigint unsigned|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="684" parent="325" name="DATA_FREE">
      <DasType>bigint unsigned|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="685" parent="325" name="AUTO_INCREMENT">
      <DasType>bigint unsigned|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="686" parent="325" name="CREATE_TIME">
      <DasType>timestamp|0s</DasType>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <column id="687" parent="325" name="UPDATE_TIME">
      <DasType>datetime|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="688" parent="325" name="CHECK_TIME">
      <DasType>datetime|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="689" parent="325" name="TABLE_COLLATION">
      <DasType>varchar(64)|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="690" parent="325" name="CHECKSUM">
      <DasType>bigint|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="691" parent="325" name="CREATE_OPTIONS">
      <DasType>varchar(256)|0s</DasType>
      <Position>20</Position>
    </column>
    <column id="692" parent="325" name="TABLE_COMMENT">
      <DasType>text|0s</DasType>
      <Position>21</Position>
    </column>
    <column id="693" parent="326" name="TABLESPACE_NAME">
      <DasType>varchar(268)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="694" parent="326" name="ENGINE_ATTRIBUTE">
      <DasType>json|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="695" parent="327" name="TABLE_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="696" parent="327" name="TABLE_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="697" parent="327" name="TABLE_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="698" parent="327" name="ENGINE_ATTRIBUTE">
      <DasType>json|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="699" parent="327" name="SECONDARY_ENGINE_ATTRIBUTE">
      <DasType>json|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="700" parent="328" name="CONSTRAINT_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="701" parent="328" name="CONSTRAINT_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="702" parent="328" name="CONSTRAINT_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="703" parent="328" name="TABLE_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="704" parent="328" name="TABLE_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="705" parent="328" name="CONSTRAINT_TYPE">
      <DasType>varchar(11)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="706" parent="328" name="ENFORCED">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="707" parent="329" name="CONSTRAINT_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="708" parent="329" name="CONSTRAINT_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="709" parent="329" name="CONSTRAINT_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="710" parent="329" name="TABLE_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="711" parent="329" name="ENGINE_ATTRIBUTE">
      <DasType>json|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="712" parent="329" name="SECONDARY_ENGINE_ATTRIBUTE">
      <DasType>json|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="713" parent="330" name="TRIGGER_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="714" parent="330" name="TRIGGER_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="715" parent="330" name="TRIGGER_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="716" parent="330" name="EVENT_MANIPULATION">
      <DasType>enum(&apos;INSERT&apos;, &apos;UPDATE&apos;, &apos;DELETE&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="717" parent="330" name="EVENT_OBJECT_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="718" parent="330" name="EVENT_OBJECT_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="719" parent="330" name="EVENT_OBJECT_TABLE">
      <DasType>varchar(64)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="720" parent="330" name="ACTION_ORDER">
      <DasType>int unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>8</Position>
    </column>
    <column id="721" parent="330" name="ACTION_CONDITION">
      <DasType>binary(0)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="722" parent="330" name="ACTION_STATEMENT">
      <DasType>longtext|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="723" parent="330" name="ACTION_ORIENTATION">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="724" parent="330" name="ACTION_TIMING">
      <DasType>enum(&apos;BEFORE&apos;, &apos;AFTER&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="725" parent="330" name="ACTION_REFERENCE_OLD_TABLE">
      <DasType>binary(0)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="726" parent="330" name="ACTION_REFERENCE_NEW_TABLE">
      <DasType>binary(0)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="727" parent="330" name="ACTION_REFERENCE_OLD_ROW">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <column id="728" parent="330" name="ACTION_REFERENCE_NEW_ROW">
      <DasType>varchar(3)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>16</Position>
    </column>
    <column id="729" parent="330" name="CREATED">
      <DasType>timestamp(2)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>17</Position>
    </column>
    <column id="730" parent="330" name="SQL_MODE">
      <DasType>set(&apos;REAL_AS_FLOAT&apos;, &apos;PIPES_AS_CONCAT&apos;, &apos;ANSI_QUOTES&apos;, &apos;IGNORE_SPACE&apos;, &apos;NOT_USED&apos;, &apos;ONLY_FULL_GROUP_BY&apos;, &apos;NO_UNSIGNED_SUBTRACTION&apos;, &apos;NO_DIR_IN_CREATE&apos;, &apos;NOT_USED_9&apos;, &apos;NOT_USED_10&apos;, &apos;NOT_USED_11&apos;, &apos;NOT_USED_12&apos;, &apos;NOT_USED_13&apos;, &apos;NOT_USED_14&apos;, &apos;NOT_USED_15&apos;, &apos;NOT_USED_16&apos;, &apos;NOT_USED_17&apos;, &apos;NOT_USED_18&apos;, &apos;ANSI&apos;, &apos;NO_AUTO_VALUE_ON_ZERO&apos;, &apos;NO_BACKSLASH_ESCAPES&apos;, &apos;STRICT_TRANS_TABLES&apos;, &apos;STRICT_ALL_TABLES&apos;, &apos;NO_ZERO_IN_DATE&apos;, &apos;NO_ZERO_DATE&apos;, &apos;ALLOW_INVALID_DATES&apos;, &apos;ERROR_FOR_DIVISION_BY_ZERO&apos;, &apos;TRADITIONAL&apos;, &apos;NOT_USED_29&apos;, &apos;HIGH_NOT_PRECEDENCE&apos;, &apos;NO_ENGINE_SUBSTITUTION&apos;, &apos;PAD_CHAR_TO_FULL_LENGTH&apos;, &apos;TIME_TRUNCATE_FRACTIONAL&apos;)|0e</DasType>
      <NotNull>1</NotNull>
      <Position>18</Position>
    </column>
    <column id="731" parent="330" name="DEFINER">
      <DasType>varchar(288)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>19</Position>
    </column>
    <column id="732" parent="330" name="CHARACTER_SET_CLIENT">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>20</Position>
    </column>
    <column id="733" parent="330" name="COLLATION_CONNECTION">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>21</Position>
    </column>
    <column id="734" parent="330" name="DATABASE_COLLATION">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>22</Position>
    </column>
    <column id="735" parent="331" name="USER">
      <DasType>char(32)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="736" parent="331" name="HOST">
      <DasType>char(255)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="737" parent="331" name="ATTRIBUTE">
      <DasType>longtext|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="738" parent="332" name="TABLE_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="739" parent="332" name="TABLE_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="740" parent="332" name="TABLE_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="741" parent="332" name="VIEW_DEFINITION">
      <DasType>longtext|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="742" parent="332" name="CHECK_OPTION">
      <DasType>enum(&apos;NONE&apos;, &apos;LOCAL&apos;, &apos;CASCADED&apos;)|0e</DasType>
      <Position>5</Position>
    </column>
    <column id="743" parent="332" name="IS_UPDATABLE">
      <DasType>enum(&apos;NO&apos;, &apos;YES&apos;)|0e</DasType>
      <Position>6</Position>
    </column>
    <column id="744" parent="332" name="DEFINER">
      <DasType>varchar(288)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="745" parent="332" name="SECURITY_TYPE">
      <DasType>varchar(7)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="746" parent="332" name="CHARACTER_SET_CLIENT">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="747" parent="332" name="COLLATION_CONNECTION">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="748" parent="333" name="TABLE_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="749" parent="333" name="TABLE_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="750" parent="333" name="TABLE_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="751" parent="333" name="SPECIFIC_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="752" parent="333" name="SPECIFIC_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="753" parent="333" name="SPECIFIC_NAME">
      <DasType>varchar(64)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="754" parent="334" name="VIEW_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="755" parent="334" name="VIEW_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="756" parent="334" name="VIEW_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="757" parent="334" name="TABLE_CATALOG">
      <DasType>varchar(64)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="758" parent="334" name="TABLE_SCHEMA">
      <DasType>varchar(64)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="759" parent="334" name="TABLE_NAME">
      <DasType>varchar(64)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="760" parent="335" name="version_num">
      <DasType>varchar(320)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <index id="761" parent="335" name="PRIMARY">
      <ColNames>version_num</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="762" parent="335" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="763" parent="336" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>消息ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="764" parent="336" name="message">
      <Comment>消息内容</Comment>
      <DasType>text|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="765" parent="336" name="message_type">
      <Comment>消息类型</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;info&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="766" parent="336" name="is_active">
      <Comment>是否激活</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="767" parent="336" name="start_time">
      <Comment>开始时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="768" parent="336" name="end_time">
      <Comment>结束时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="769" parent="336" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="770" parent="336" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>8</Position>
    </column>
    <index id="771" parent="336" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="772" parent="336" name="idx_message_type">
      <ColNames>message_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="773" parent="336" name="idx_is_active">
      <ColNames>is_active</ColNames>
      <Type>btree</Type>
    </index>
    <index id="774" parent="336" name="idx_start_time">
      <ColNames>start_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="775" parent="336" name="idx_end_time">
      <ColNames>end_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="776" parent="336" name="idx_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="777" parent="336" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="778" parent="337" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>内容ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="779" parent="337" name="content_type">
      <Comment>内容类型</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="780" parent="337" name="title">
      <Comment>标题</Comment>
      <DasType>varchar(200)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="781" parent="337" name="content">
      <Comment>内容</Comment>
      <DasType>text|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="782" parent="337" name="is_published">
      <Comment>是否已发布</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="783" parent="337" name="publish_at">
      <Comment>发布时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="784" parent="337" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="785" parent="337" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>8</Position>
    </column>
    <index id="786" parent="337" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="787" parent="337" name="idx_content_type">
      <ColNames>content_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="788" parent="337" name="idx_is_published">
      <ColNames>is_published</ColNames>
      <Type>btree</Type>
    </index>
    <index id="789" parent="337" name="idx_publish_at">
      <ColNames>publish_at</ColNames>
      <Type>btree</Type>
    </index>
    <index id="790" parent="337" name="idx_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="791" parent="337" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="792" parent="338" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>明细ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="793" parent="338" name="ranking_id">
      <Comment>榜单ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="794" parent="338" name="participant_name">
      <Comment>参与者姓名</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="795" parent="338" name="completion_seconds">
      <Comment>完成时间（秒）</Comment>
      <DasType>int|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="796" parent="338" name="rank_range">
      <Comment>排名范围</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="797" parent="338" name="notes">
      <Comment>备注</Comment>
      <DasType>text|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="798" parent="338" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="799" parent="338" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>8</Position>
    </column>
    <column id="800" parent="338" name="team_name">
      <Comment>队伍名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="801" parent="338" name="rank_start">
      <Comment>排名开始</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="802" parent="338" name="rank_end">
      <Comment>排名结束</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="803" parent="338" name="completion_time">
      <Comment>完成时间(分秒)</Comment>
      <DasType>time|0s</DasType>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="804" parent="338" name="participant_count">
      <Comment>当前时间区间参与人数</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>13</Position>
    </column>
    <column id="805" parent="338" name="team_info">
      <Comment>队伍信息(JSON格式)</Comment>
      <DasType>text|0s</DasType>
      <Position>14</Position>
    </column>
    <foreign-key id="806" parent="338" name="ranking_details_ibfk_1">
      <ColNames>ranking_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>rankings</RefTableName>
    </foreign-key>
    <index id="807" parent="338" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="808" parent="338" name="idx_ranking_id">
      <ColNames>ranking_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="809" parent="338" name="idx_completion_seconds">
      <ColNames>completion_seconds</ColNames>
      <Type>btree</Type>
    </index>
    <index id="810" parent="338" name="idx_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="811" parent="338" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="812" parent="339" name="id">
      <AutoIncrement>5</AutoIncrement>
      <Comment>榜单ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="813" parent="339" name="name">
      <Comment>榜单标题</Comment>
      <DasType>varchar(200)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="814" parent="339" name="description">
      <Comment>榜单描述</Comment>
      <DasType>text|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="815" parent="339" name="ranking_type">
      <Comment>榜单类型</Comment>
      <DasType>varchar(20)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="816" parent="339" name="status">
      <Comment>榜单状态</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;draft&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="817" parent="339" name="team_size_limit">
      <Comment>组队人数限制</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="818" parent="339" name="total_participants">
      <Comment>当前参与人数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="819" parent="339" name="start_time">
      <Comment>开始时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="820" parent="339" name="end_time">
      <Comment>结束时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="821" parent="339" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="822" parent="339" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>11</Position>
    </column>
    <column id="823" parent="339" name="period">
      <DasType>tinyint|0s</DasType>
      <Position>12</Position>
    </column>
    <index id="824" parent="339" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="825" parent="339" name="idx_ranking_type">
      <ColNames>ranking_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="826" parent="339" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="827" parent="339" name="idx_start_time">
      <ColNames>start_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="828" parent="339" name="idx_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="829" parent="339" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="830" parent="340" name="id">
      <AutoIncrement>1</AutoIncrement>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="831" parent="340" name="name">
      <Comment>赞助商名称</Comment>
      <DasType>varchar(200)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="832" parent="340" name="logo_url">
      <Comment>Logo URL（用作头像）</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="833" parent="340" name="sort_order">
      <Comment>排序顺序</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="834" parent="340" name="is_active">
      <Comment>是否启用</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="835" parent="340" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="836" parent="340" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <index id="837" parent="340" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="838" parent="340" name="idx_sponsors_new_sort_order">
      <ColNames>sort_order</ColNames>
      <Type>btree</Type>
    </index>
    <index id="839" parent="340" name="idx_sponsors_new_is_active">
      <ColNames>is_active</ColNames>
      <Type>btree</Type>
    </index>
    <index id="840" parent="340" name="idx_sponsors_new_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="841" parent="340" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="842" parent="341" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>配置ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="843" parent="341" name="config_key">
      <Comment>配置键</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="844" parent="341" name="config_value">
      <Comment>配置值</Comment>
      <DasType>text|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="845" parent="341" name="config_type">
      <Comment>配置类型</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;string&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="846" parent="341" name="description">
      <Comment>配置描述</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="847" parent="341" name="is_public">
      <Comment>是否公开（前端可访问）</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="848" parent="341" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
    </column>
    <column id="849" parent="341" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>8</Position>
    </column>
    <index id="850" parent="341" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="851" parent="341" name="config_key">
      <ColNames>config_key</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="852" parent="341" name="idx_config_key">
      <ColNames>config_key</ColNames>
      <Type>btree</Type>
    </index>
    <index id="853" parent="341" name="idx_config_type">
      <ColNames>config_type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="854" parent="341" name="idx_is_public">
      <ColNames>is_public</ColNames>
      <Type>btree</Type>
    </index>
    <key id="855" parent="341" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="856" parent="341" name="config_key">
      <UnderlyingIndexName>config_key</UnderlyingIndexName>
    </key>
    <check id="857" parent="342" name="check_age_range">
      <Predicate>(`age` is null) or ((`age` &gt;= 0) and (`age` &lt;= 150))</Predicate>
    </check>
    <column id="858" parent="342" name="id">
      <AutoIncrement>2</AutoIncrement>
      <Comment>用户ID</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="859" parent="342" name="username">
      <Comment>用户名</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="860" parent="342" name="password_hash">
      <Comment>密码哈希</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="861" parent="342" name="nickname">
      <Comment>昵称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <column id="862" parent="342" name="avatar_url">
      <Comment>头像URL</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="863" parent="342" name="email">
      <Comment>邮箱</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="864" parent="342" name="phone">
      <Comment>手机号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="865" parent="342" name="wechat_openid">
      <Comment>微信OpenID</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="866" parent="342" name="wechat_unionid">
      <Comment>微信UnionID</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="867" parent="342" name="role">
      <Comment>用户角色</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;user&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="868" parent="342" name="is_active">
      <Comment>是否激活</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
    </column>
    <column id="869" parent="342" name="is_verified">
      <Comment>是否验证</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
    </column>
    <column id="870" parent="342" name="bio">
      <Comment>个人简介</Comment>
      <DasType>text|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="871" parent="342" name="level">
      <Comment>用户等级</Comment>
      <DasType>varchar(50)|0s</DasType>
      <DefaultExpression>&apos;江湖新人&apos;</DefaultExpression>
      <Position>14</Position>
    </column>
    <column id="872" parent="342" name="points">
      <Comment>积分</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>15</Position>
    </column>
    <column id="873" parent="342" name="created_at">
      <Comment>创建时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
    </column>
    <column id="874" parent="342" name="updated_at">
      <Comment>更新时间</Comment>
      <DasType>timestamp|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <NotNull>1</NotNull>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>17</Position>
    </column>
    <column id="875" parent="342" name="last_login_at">
      <Comment>最后登录时间</Comment>
      <DasType>timestamp|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="876" parent="342" name="location">
      <Comment>所在地</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="877" parent="342" name="user_number">
      <Comment>用户编号</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>20</Position>
    </column>
    <column id="878" parent="342" name="gender">
      <Comment>性别</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>21</Position>
    </column>
    <column id="879" parent="342" name="age">
      <Comment>年龄</Comment>
      <DasType>int|0s</DasType>
      <Position>22</Position>
    </column>
    <index id="880" parent="342" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="881" parent="342" name="username">
      <ColNames>username</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="882" parent="342" name="email">
      <ColNames>email</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="883" parent="342" name="phone">
      <ColNames>phone</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="884" parent="342" name="wechat_openid">
      <ColNames>wechat_openid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="885" parent="342" name="wechat_unionid">
      <ColNames>wechat_unionid</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="886" parent="342" name="idx_users_user_number">
      <ColNames>user_number</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="887" parent="342" name="idx_username">
      <ColNames>username</ColNames>
      <Type>btree</Type>
    </index>
    <index id="888" parent="342" name="idx_email">
      <ColNames>email</ColNames>
      <Type>btree</Type>
    </index>
    <index id="889" parent="342" name="idx_wechat_openid">
      <ColNames>wechat_openid</ColNames>
      <Type>btree</Type>
    </index>
    <index id="890" parent="342" name="idx_role">
      <ColNames>role</ColNames>
      <Type>btree</Type>
    </index>
    <index id="891" parent="342" name="idx_is_active">
      <ColNames>is_active</ColNames>
      <Type>btree</Type>
    </index>
    <index id="892" parent="342" name="idx_created_at">
      <ColNames>created_at</ColNames>
      <Type>btree</Type>
    </index>
    <key id="893" parent="342" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="894" parent="342" name="username">
      <UnderlyingIndexName>username</UnderlyingIndexName>
    </key>
    <key id="895" parent="342" name="email">
      <UnderlyingIndexName>email</UnderlyingIndexName>
    </key>
    <key id="896" parent="342" name="phone">
      <UnderlyingIndexName>phone</UnderlyingIndexName>
    </key>
    <key id="897" parent="342" name="wechat_openid">
      <UnderlyingIndexName>wechat_openid</UnderlyingIndexName>
    </key>
    <key id="898" parent="342" name="wechat_unionid">
      <UnderlyingIndexName>wechat_unionid</UnderlyingIndexName>
    </key>
    <key id="899" parent="342" name="idx_users_user_number">
      <UnderlyingIndexName>idx_users_user_number</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>